import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Filter,
  Search,
  SlidersHorizontal,
  Download,
  Upload,
  Plus,
  Lock,
} from "lucide-react";
import { ColumnVisibility } from "../types";
import { suppliersColumnConfig } from "../config/columnConfig";
import { SupplierImportExport } from "./SupplierImportExport";
import Link from "next/link";
import { useContactLimits } from "@/hooks/useSubscriptionLimits";
import { toast } from "sonner";

interface SupplierActionsProps {
  columnVisibility: ColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<ColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>;
  onFilterClick: () => void;
  onImportClick: () => void;
  onExportClick: () => void;
  onRefresh?: () => void; // Add refresh callback for after import
}

export const SupplierActions: React.FC<SupplierActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  onFilterClick,
  onImportClick,
  onExportClick,
  onRefresh,
}) => {
  const {
    canCreateContact,
    contactMessage,
    currentContactUsage,
    contactLimit,
    isLoading: limitsLoading,
  } = useContactLimits();

  const handleAddSupplierClick = (e: React.MouseEvent) => {
    if (!canCreateContact) {
      e.preventDefault();
      toast.error(contactMessage || "Batas kontak tercapai untuk paket Anda.");
      return;
    }
  };
  return (
    <div className="flex flex-wrap items-center justify-between gap-4">
      <div className="flex flex-wrap items-center gap-2">
        {/* Column Visibility Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer">
            <SlidersHorizontal className="mr-2 h-4 w-4" />
            Kolom
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Tampilkan Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {suppliersColumnConfig.map((column) => (
              <DropdownMenuCheckboxItem
                key={column.key}
                checked={columnVisibility[column.key]}
                onCheckedChange={(checked) =>
                  setColumnVisibility((prev) => ({
                    ...prev,
                    [column.key]: !!checked,
                  }))
                }
                onSelect={(e) => e.preventDefault()}
              >
                {column.label}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Filter Button */}
        <Button
          variant="outline"
          size="sm"
          className="h-9"
          onClick={onFilterClick}
        >
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>

        {/* Import/Export Component */}
        <SupplierImportExport onRefresh={onRefresh} />

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400" />
          <Input
            type="search"
            placeholder="Cari supplier..."
            className="w-64 pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Add Supplier Button */}
        {canCreateContact ? (
          <Link
            href="/dashboard/contacts/new"
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer"
          >
            <Plus className="mr-2 h-5 w-5" />
            Tambah
          </Link>
        ) : (
          <Button
            disabled
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm cursor-not-allowed"
            onClick={handleAddSupplierClick}
            title={contactMessage || "Batas kontak tercapai"}
          >
            <Lock className="mr-2 h-5 w-5" />
            Tambah
          </Button>
        )}
      </div>
    </div>
  );
};
